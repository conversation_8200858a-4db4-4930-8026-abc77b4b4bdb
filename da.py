#!/usr/bin/env python3
"""
Density Altitude Fetcher Module
Converts da.sh functionality to Python module
Fetches density altitude data from FlightAware for a given airport ID
"""

import warnings
# Suppress urllib3 SSL warnings before importing requests
warnings.filterwarnings('ignore', message='urllib3 v2 only supports OpenSSL 1.1.1+')

import requests
import re
from datetime import datetime, timezone
import pytz

# Additional urllib3 warning suppression
try:
    import urllib3
    urllib3.disable_warnings()
except ImportError:
    pass


def fetch_density_altitude(airport_id):
    """
    Fetch density altitude data for a given airport ID from FlightAware
    
    Args:
        airport_id (str): Airport identifier (e.g., 'PHOG')
        
    Returns:
        dict: Dictionary containing:
            - id: Airport ID
            - density_altitude: Density altitude in feet (numeric)
            - local_stamp: Local timestamp string
            - utc_stamp: UTC timestamp string  
            - utc_last_fetch: UTC timestamp when data was fetched
    """
    
    # Current UTC time for fetch timestamp
    utc_now = datetime.now(timezone.utc)
    utc_last_fetch = utc_now.strftime("%Y-%m-%dT%H:%M:%S")
    utc_stamp = utc_last_fetch  # Will be updated if we find actual data timestamp
    
    # Convert to Hawaii local time for local_stamp
    hawaii_tz = pytz.timezone("Pacific/Honolulu")
    local_now = utc_now.astimezone(hawaii_tz)
    local_stamp = local_now.strftime("%Y-%m-%dT%H:%M:%S")
    
    try:
        # Fetch the FlightAware weather page for the airport
        url = f"https://flightaware.com/resources/airport/{airport_id}/weather"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Mobile Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=20)
        response.raise_for_status()
        
        html_content = response.text
        
        # Extract density altitude using regex pattern similar to the shell script
        # The original script uses: grep "\sft" |head -1 |awk -F '>' '{print $4}' |awk '{print $1}'|sed s/,//
        # This means: find lines with " ft", take first line, split by '>', take 4th field, take first word, remove commas

        density_altitude = 0

        # Split HTML into lines and look for lines containing " ft"
        lines = html_content.split('\n')
        ft_lines = [line for line in lines if ' ft' in line]

        if ft_lines:
            first_ft_line = ft_lines[0]

            # Apply the original shell script logic: split by '>', take 4th field (index 3)
            parts = first_ft_line.split('>')
            if len(parts) > 3:
                fourth_part = parts[3]

                # Take first word and remove commas
                first_word = fourth_part.split()[0] if fourth_part.split() else ""
                clean_word = first_word.replace(',', '')

                # Try to convert to integer
                try:
                    if clean_word.isdigit():
                        density_altitude = int(clean_word)
                except ValueError:
                    pass

        # If the original approach didn't work, try more specific patterns
        if density_altitude == 0:
            # Try multiple patterns to find density altitude
            patterns = [
                r'Density\s+Altitude[^>]*>([0-9,]+)\s*ft',
                r'density\s+altitude[^>]*>([0-9,]+)\s*ft',
                r'>([0-9,]+)\s*ft<.*density',
                r'([0-9,]+)\s*ft.*density\s*altitude',
                r'\s([0-9,]+)\s*ft\s'  # Generic pattern for any number followed by "ft"
            ]

            for pattern in patterns:
                match = re.search(pattern, html_content, re.IGNORECASE)
                if match:
                    # Remove commas and convert to integer
                    density_altitude_str = match.group(1).replace(',', '')
                    try:
                        density_altitude = int(density_altitude_str)
                        break
                    except ValueError:
                        continue
        
        # Try to extract timestamp from the weather data if available
        # Look for common timestamp patterns in weather reports
        timestamp_patterns = [
            r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z)',
            r'(\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2})',
            r'as\s+of\s+(\d{2}:\d{2})\s+UTC',
            r'(\d{2}:\d{2})\s+GMT'
        ]
        
        for pattern in timestamp_patterns:
            match = re.search(pattern, html_content)
            if match:
                timestamp_str = match.group(1)
                try:
                    # Try to parse different timestamp formats
                    if 'T' in timestamp_str and timestamp_str.endswith('Z'):
                        # ISO format with Z
                        dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                        utc_stamp = dt.strftime("%Y-%m-%dT%H:%M:%S")
                        local_stamp = dt.astimezone(hawaii_tz).strftime("%Y-%m-%dT%H:%M:%S")
                    elif '/' in timestamp_str:
                        # MM/DD/YYYY HH:MM format
                        dt = datetime.strptime(timestamp_str, "%m/%d/%Y %H:%M")
                        dt = dt.replace(tzinfo=timezone.utc)
                        utc_stamp = dt.strftime("%Y-%m-%dT%H:%M:%S")
                        local_stamp = dt.astimezone(hawaii_tz).strftime("%Y-%m-%dT%H:%M:%S")
                    elif ':' in timestamp_str and len(timestamp_str) == 5:
                        # HH:MM format - assume today's date
                        time_parts = timestamp_str.split(':')
                        hour, minute = int(time_parts[0]), int(time_parts[1])
                        dt = utc_now.replace(hour=hour, minute=minute, second=0, microsecond=0)
                        utc_stamp = dt.strftime("%Y-%m-%dT%H:%M:%S")
                        local_stamp = dt.astimezone(hawaii_tz).strftime("%Y-%m-%dT%H:%M:%S")
                    break
                except Exception as e:
                    # If timestamp parsing fails, keep the fetch time
                    print(f"Warning: Could not parse timestamp '{timestamp_str}': {e}")
                    continue
        
        # Build result dictionary
        result = {
            "id": airport_id,
            "density_altitude": density_altitude,
            "local_stamp": local_stamp,
            "utc_stamp": utc_stamp,
            "utc_last_fetch": utc_last_fetch
        }
        
        return result
        
    except Exception as e:
        print(f"Error fetching density altitude for {airport_id}: {e}")
        
        # Return error result with zero density altitude
        return {
            "id": airport_id,
            "density_altitude": 0,
            "local_stamp": local_stamp,
            "utc_stamp": utc_stamp,
            "utc_last_fetch": utc_last_fetch
        }


def main():
    """
    Main function for testing the module
    """
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python da.py <AIRPORT_ID>")
        print("Example: python da.py PHOG")
        sys.exit(1)
    
    airport_id = sys.argv[1].upper()
    result = fetch_density_altitude(airport_id)
    
    print(f"Density Altitude Data for {airport_id}:")
    print(f"  Airport ID: {result['id']}")
    print(f"  Density Altitude: {result['density_altitude']} ft")
    print(f"  Local Time: {result['local_stamp']}")
    print(f"  UTC Time: {result['utc_stamp']}")
    print(f"  Fetched at (UTC): {result['utc_last_fetch']}")
    
    return result


if __name__ == "__main__":
    main()
