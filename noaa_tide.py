#!/usr/bin/env python3
"""
NOAA Tide Data Fetcher
Similar to noaa_ndbc.py but for NOAA tide data
Fetches tide predictions from NOAA Tides and Currents API
"""

import warnings
# Suppress urllib3 SSL warnings before importing requests
warnings.filterwarnings('ignore', message='urllib3 v2 only supports OpenSSL 1.1.1+')

import requests
import json
from datetime import datetime, timezone
import pytz

# Additional urllib3 warning suppression
try:
    import urllib3
    urllib3.disable_warnings()
except ImportError:
    pass


def fetch_tide_data(spots_dict):
    """
    Fetch and process tide data for all spots in spots_dict
    Combined function that fetches and immediately processes each tide station

    Args:
        spots_dict: Dictionary in same format as ik_multi
                   Keys are spot names, values contain 'id' field with station ID

    Returns:
        Dictionary with processed data for each spot
    """
    processed_data = {}
    
    # NOAA API URL for high/low tide predictions
    NOAA_API_URL_HILO = "https://api.tidesandcurrents.noaa.gov/api/prod/datagetter?date=today&station={}&product=predictions&datum=MLLW&time_zone=lst_ldt&units=english&format=json&interval=hilo"

    for spot_name, spot_info in spots_dict.items():
        station_id = spot_info.get('id')
        if not station_id:
            print(f"No station ID found for {spot_name}")
            continue

        print(f"Fetching tide data for {spot_name} (station {station_id})")

        try:
            # Fetch tide data from NOAA API
            url = NOAA_API_URL_HILO.format(station_id)
            print(f"  URL: {url}")
            
            response = requests.get(url, timeout=20)
            response.raise_for_status()
            
            tides = response.json()
            
            if 'predictions' not in tides or not tides['predictions']:
                print(f"  Warning: No tide predictions found for {spot_name}")
                continue
            
            # Current UTC time for fetch timestamp
            utc_now = datetime.now(timezone.utc)
            utc_last_fetch = utc_now.strftime("%Y-%m-%dT%H:%M:%S")
            utc_stamp = utc_last_fetch  # Will be updated if we find actual data timestamp
            
            # Convert to local time for stamp field (assuming Hawaii timezone for tides)
            hawaii_tz = pytz.timezone("Pacific/Honolulu")
            local_now = utc_now.astimezone(hawaii_tz)
            local_stamp = local_now.strftime("%Y-%m-%dT%H:%M:%S")
            
            # Handle basename and label
            if "filename" in spot_info:
                basename = spot_info['filename']
            elif "name" in spot_info:
                basename = spot_info['name']
            else:
                basename = spot_name

            if "name" in spot_info:
                label = spot_info['name'].replace('_', ' ').title()
            else:
                label = spot_name.replace('_', ' ').title()
            
            # Process tide predictions
            formatted_array = []
            t_array = []  # For the main two tides during daylight hours
            
            for item in tides["predictions"]:
                datetime_obj = datetime.strptime(item["t"], '%Y-%m-%d %H:%M')
                
                # Format tide data
                formatted_item = {}
                formatted_item["type"] = item["type"]
                formatted_item["v"] = round(float(item["v"]), 1)
                
                # Handle negative tides (use 'N' instead of 'L' for negative)
                if float(item["v"]) < 0:
                    formatted_item["v"] = str(formatted_item["v"]).replace('-', '')
                    formatted_item["type"] = 'N'
                
                formatted_item["t"] = datetime_obj.strftime('%H%M')
                formatted_item["time_24"] = datetime_obj.strftime('%H:%M')
                formatted_item["raw_value"] = float(item["v"])
                
                formatted_array.append(formatted_item)
                
                # Skip tides before 6 AM for the main t_array
                if datetime_obj.hour < 6:
                    continue
                
                # Add to main tide array (limit to 2 for daylight hours)
                if len(t_array) < 2:
                    t_array.append(formatted_item)
            
            # If we don't have 2 daylight tides, use the first 2 overall
            if len(t_array) < 2 and len(formatted_array) > 1:
                t_array = formatted_array[0:2]
            
            # Create tide string for main tides
            if len(t_array) > 1:
                tide_string = f"{t_array[0]['t']}{t_array[0]['type']}{t_array[0]['v']}~{t_array[1]['t']}{t_array[1]['type']}{t_array[1]['v']}"
            elif len(t_array) > 0:
                tide_string = f"{t_array[0]['t']}{t_array[0]['type']}{t_array[0]['v']}"
            else:
                tide_string = "No tide data"
            
            # Create full tide string with all tides
            full_tide_parts = []
            for tide in formatted_array:
                full_tide_parts.append(f"{tide['time_24']} {tide['type']} {tide['v']} ft")
            full_tide_string = " ~ ".join(full_tide_parts)
            
            # Try to extract timestamp from first prediction
            if tides['predictions']:
                try:
                    first_prediction_time = tides['predictions'][0]['t']
                    # Parse the date portion for the stamp
                    date_part = first_prediction_time.split(' ')[0]
                    # Convert to ISO format for consistency
                    dt = datetime.strptime(date_part, '%Y-%m-%d')
                    utc_stamp = dt.strftime("%Y-%m-%dT00:00:00")
                except Exception as e:
                    print(f"  Warning: Could not parse prediction timestamp: {e}")
            
            # Build data dictionary in tide format
            spot_data = {
                "stamp": local_stamp,
                "tide_string": tide_string,
                "full_tide_string": full_tide_string,
                "num_tides": len(formatted_array),
                "label": label,
                "type": "tides",
                "utc_stamp": utc_stamp,
                "utc_last_fetch": utc_last_fetch,
                "station_id": station_id
            }
            
            # Add individual tide entries
            for i, tide in enumerate(formatted_array):
                spot_data[f"tide{i}"] = f"{tide['time_24']} {tide['type']} {tide['raw_value']} ft"
            
            # Store in results
            processed_data[basename] = spot_data
            
            print(f"  Processed {len(formatted_array)} tide predictions")
            print(f"  Main tides: {tide_string}")

        except Exception as e:
            print(f"Error fetching/processing tide data for {spot_name}: {e}")

    return processed_data


def main(spots_dict, debug=False):
    """
    Main function to fetch and process NOAA tide data

    Args:
        spots_dict: Dictionary of spots in same format as ik_multi
        debug: If True, use debug data instead of fetching

    Returns:
        Dictionary with processed data for each spot
    """

    if not debug:
        return fetch_tide_data(spots_dict)
    else:
        print("Using debug data for NOAA Tides")
        processed_data = {}

        for spot_name, spot_info in spots_dict.items():
            utc_stamp = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S")
            local_stamp = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
            utc_last_fetch = utc_stamp

            # Handle basename and label
            if "filename" in spot_info:
                basename = spot_info['filename']
            elif "name" in spot_info:
                basename = spot_info['name']
            else:
                basename = spot_name

            if "name" in spot_info:
                label = spot_info['name'].replace('_', ' ').title()
            else:
                label = spot_name.replace('_', ' ').title()

            # Debug tide data
            spot_data = {
                "stamp": local_stamp,
                "tide_string": "0619H1.6~1105N5.5",
                "full_tide_string": "06:19 H 1.6 ft ~ 11:05 L -5.5 ft ~ 17:10 H 12.9 ft ~ 22:52 L -1.1 ft",
                "num_tides": 4,
                "label": label,
                "type": "tides",
                "utc_stamp": utc_stamp,
                "utc_last_fetch": utc_last_fetch,
                "station_id": spot_info.get('id', '1615680'),
                "tide0": "06:19 H 1.6 ft",
                "tide1": "11:05 L -5.5 ft",
                "tide2": "17:10 H 12.9 ft",
                "tide3": "22:52 L -1.1 ft"
            }

            processed_data[basename] = spot_data

        return processed_data


if __name__ == "__main__":
    import sys

    if len(sys.argv) >= 3:
        # Command line usage: python3 noaa_tide.py <station_id> <spot_name> [debug]
        station_id = sys.argv[1]
        spot_name = sys.argv[2]
        debug_mode = len(sys.argv) > 3 and sys.argv[3].lower() == 'debug'

        test_spots = {
            spot_name: {
                'id': station_id,
                'data_types': ['tides'],
                'provider': 'noaa_tide',
                'type': 'tide_station'
            }
        }

        print(f"Testing NOAA Tides for station {station_id} (spot: {spot_name})")
        if debug_mode:
            print("Using debug mode")

    else:
        # Default test with Kahului Harbor
        test_spots = {
            'kahului_harbor': {
                'id': '1615680',
                'data_types': ['tides'],
                'provider': 'noaa_tide',
                'type': 'tide_station'
            }
        }
        debug_mode = False
        print("Using default test data (Kahului Harbor, station 1615680)")
        print("Usage: python3 noaa_tide.py <station_id> <spot_name> [debug]")
        print("Example: python3 noaa_tide.py 1615680 kahului_harbor")

    result = main(spots_dict=test_spots, debug=debug_mode)
    print("\nResults:")
    for spot_name, data in result.items():
        print(f"\n{data['label']}:")
        print(f"  Main Tides: {data['tide_string']}")
        print(f"  Full Tides: {data['full_tide_string']}")
        print(f"  Number of Tides: {data['num_tides']}")
        print(f"  Station ID: {data['station_id']}")
        print(f"  UTC Timestamp: {data['utc_stamp']}")
        print(f"  Local Timestamp: {data['stamp']}")

        # Write JSON file for testing
        json_filename = f"{spot_name}.json"
        with open(json_filename, "w") as f:
            json.dump(data, f, indent=2)
        print(f"  Wrote: {json_filename}")

        print(data)
